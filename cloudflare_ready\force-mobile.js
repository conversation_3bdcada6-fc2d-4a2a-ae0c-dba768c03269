/**
 * إجبار الصفحة على العرض الصحيح للهواتف
 * Force Mobile Display - Fixes oversized mobile display
 */

(function() {
    'use strict';
    
    // إجبار viewport الصحيح
    function forceCorrectViewport() {
        // إزالة viewport الموجود
        const existingViewport = document.querySelector('meta[name="viewport"]');
        if (existingViewport) {
            existingViewport.remove();
        }
        
        // إضافة viewport جديد صحيح
        const viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no';
        document.head.insertBefore(viewport, document.head.firstChild);
    }
    
    // إجبار الحجم الصحيح
    function forceCorrectSize() {
        const html = document.documentElement;
        const body = document.body;
        
        // إجبار الحجم على HTML
        html.style.width = '100vw';
        html.style.maxWidth = '100vw';
        html.style.overflowX = 'hidden';
        html.style.webkitTextSizeAdjust = '100%';
        html.style.msTextSizeAdjust = '100%';
        html.style.textSizeAdjust = '100%';
        
        // إجبار الحجم على Body
        body.style.width = '100vw';
        body.style.maxWidth = '100vw';
        body.style.minWidth = '100vw';
        body.style.overflowX = 'hidden';
        body.style.margin = '0';
        body.style.padding = '0';
        body.style.boxSizing = 'border-box';
        
        // منع التكبير
        body.style.zoom = '1';
        body.style.webkitTransform = 'scale(1)';
        body.style.transform = 'scale(1)';
    }
    
    // إصلاح الحاويات
    function fixContainers() {
        const containers = document.querySelectorAll('.container, .container.mx-auto, #main-content');
        containers.forEach(container => {
            container.style.width = '100vw';
            container.style.maxWidth = '100vw';
            container.style.minWidth = '100vw';
            container.style.paddingLeft = '10px';
            container.style.paddingRight = '10px';
            container.style.marginLeft = '0';
            container.style.marginRight = '0';
            container.style.overflowX = 'hidden';
            container.style.boxSizing = 'border-box';
        });
    }
    
    // إصلاح الشبكة
    function fixGrid() {
        const grids = document.querySelectorAll('.grid-cols-2');
        grids.forEach(grid => {
            grid.style.width = '100%';
            grid.style.maxWidth = '100%';
            grid.style.display = 'grid';
            grid.style.boxSizing = 'border-box';
            
            if (window.innerWidth <= 480) {
                grid.style.gridTemplateColumns = '1fr';
                grid.style.gap = '5px';
            } else if (window.innerWidth <= 768) {
                grid.style.gridTemplateColumns = '1fr 1fr';
                grid.style.gap = '8px';
            }
            
            // إصلاح عناصر الشبكة
            const gridItems = grid.children;
            Array.from(gridItems).forEach(item => {
                item.style.width = '100%';
                item.style.minWidth = '0';
                item.style.boxSizing = 'border-box';
                if (window.innerWidth <= 768) {
                    item.style.padding = '8px';
                }
            });
        });
    }
    
    // إصلاح الصور
    function fixImages() {
        const images = document.querySelectorAll('img, #main-mod-image');
        images.forEach(img => {
            img.style.width = '100%';
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.objectFit = 'cover';
            img.style.display = 'block';
        });
    }
    
    // إزالة الألوان المزعجة
    function removeAnnoyingColors() {
        const elements = document.querySelectorAll('.main-image-glow, .thumbnail-container, .thumbnail-container-centered');
        elements.forEach(element => {
            element.style.background = 'transparent';
            element.style.backgroundColor = 'transparent';
        });
        
        // إخفاء التأثيرات المزعجة
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => {
            particle.style.display = 'none';
        });
    }
    
    // إصلاح الأزرار
    function fixButtons() {
        const buttons = document.querySelectorAll('.pixel-button, .download-button, button');
        buttons.forEach(button => {
            if (window.innerWidth <= 768) {
                button.style.width = '100%';
                button.style.maxWidth = '100%';
                button.style.padding = '12px';
                button.style.fontSize = '14px';
                button.style.margin = '10px 0';
                button.style.boxSizing = 'border-box';
            }
        });
    }
    
    // إصلاح النصوص
    function fixTexts() {
        if (window.innerWidth <= 768) {
            const titles = document.querySelectorAll('h1, .mod-title');
            titles.forEach(title => {
                title.style.fontSize = '18px';
                title.style.lineHeight = '1.3';
                title.style.margin = '10px 0';
            });
            
            const labels = document.querySelectorAll('.info-label');
            labels.forEach(label => {
                label.style.fontSize = '12px';
            });
            
            const infos = document.querySelectorAll('.mod-info');
            infos.forEach(info => {
                info.style.fontSize = '14px';
            });
        }
    }
    
    // إصلاح شامل
    function applyAllFixes() {
        forceCorrectViewport();
        forceCorrectSize();
        fixContainers();
        fixGrid();
        fixImages();
        removeAnnoyingColors();
        fixButtons();
        fixTexts();
        
        // إضافة CSS إضافي
        const style = document.createElement('style');
        style.textContent = `
            * { max-width: 100vw !important; box-sizing: border-box !important; }
            html, body { overflow-x: hidden !important; width: 100vw !important; }
            .container { width: 100vw !important; padding: 10px !important; }
            @media (max-width: 768px) {
                .grid-cols-2 { grid-template-columns: 1fr 1fr !important; gap: 8px !important; }
                .container { padding: 5px !important; }
            }
            @media (max-width: 480px) {
                .grid-cols-2 { grid-template-columns: 1fr !important; gap: 5px !important; }
                .container { padding: 3px !important; }
            }
        `;
        document.head.appendChild(style);
        
        console.log('✅ تم تطبيق إصلاحات الهاتف بنجاح');
    }
    
    // تشغيل الإصلاحات فوراً
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyAllFixes);
    } else {
        applyAllFixes();
    }
    
    // تشغيل الإصلاحات عند تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        setTimeout(applyAllFixes, 100);
    });
    
    // تشغيل الإصلاحات عند تغيير الاتجاه
    window.addEventListener('orientationchange', function() {
        setTimeout(applyAllFixes, 200);
    });
    
    // تشغيل الإصلاحات بشكل دوري للتأكد
    setInterval(function() {
        if (window.innerWidth <= 768) {
            forceCorrectSize();
            fixContainers();
            removeAnnoyingColors();
        }
    }, 2000);
    
    // إصلاح فوري عند التحميل
    setTimeout(applyAllFixes, 100);
    setTimeout(applyAllFixes, 500);
    setTimeout(applyAllFixes, 1000);
    
})();
