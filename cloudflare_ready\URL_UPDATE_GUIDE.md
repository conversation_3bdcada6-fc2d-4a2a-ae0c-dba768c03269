# دليل تحديث رابط الصفحة 🔗

## الرابط الحالي المستخدم 📍
```
https://1c547fe5.sendaddons.pages.dev
```

## الأماكن التي تحتاج إلى تحديث الرابط 📝

### 1. **ملف `main.py`** - الأهم ⭐⭐⭐

#### أ) دالة `get_web_server_url()` - السطر 362:
```python
def get_web_server_url():
    """الحصول على رابط خادم الويب - يستخدم الموقع المستضاف الثابت"""
    # استخدام الموقع المستضاف الثابت بدلاً من ngrok
    hosted_url = "https://1c547fe5.sendaddons.pages.dev"  # ← غير هذا
    logger.info(f"Using hosted website: {hosted_url}")
    return hosted_url
```

#### ب) دالة `user_preview_page()` - السطر 4399:
```python
# إنشاء رابط المعاينة - استخدام index.html مع معاملات المعاينة
preview_url = f"https://1c547fe5.sendaddons.pages.dev/?preview=1&user_id={user_id}"  # ← غير هذا
```

#### ج) دالة `app_download_menu()` - السطر 9923 و 9958:
```python
🌐 <b>الويب:</b>
https://1c547fe5.sendaddons.pages.dev  # ← غير هذا

🌐 <b>Web:</b>
https://1c547fe5.sendaddons.pages.dev  # ← غير هذا
```

#### د) دالة `app_download_menu()` - السطر 9976:
```python
[InlineKeyboardButton(text["web_version"], url="https://1c547fe5.sendaddons.pages.dev")],  # ← غير هذا
```

#### هـ) دالة `send_mod_to_channel()` - السطر 13508:
```python
# استخدام الموقع المستضاف الثابت بدلاً من ngrok
base_web_server_url = "https://1c547fe5.sendaddons.pages.dev"  # ← غير هذا
```

#### و) دالة `send_mod_to_user()` - السطر 13640:
```python
# استخدام الموقع المستضاف الثابت
base_web_server_url = "https://1c547fe5.sendaddons.pages.dev"  # ← غير هذا
```

### 2. **ملف `app.json`** - للاستضافة ⭐⭐

#### السطر 40:
```json
"WEB_SERVER_URL": {
  "description": "رابط صفحة التحميل",
  "value": "https://1c547fe5.sendaddons.pages.dev"  // ← غير هذا
}
```

### 3. **ملف `.env.example`** - للمطورين ⭐

#### السطر 73:
```env
WEB_SERVER_URL=https://1c547fe5.sendaddons.pages.dev  # ← غير هذا
```

### 4. **ملف `QUICK_DEPLOY.md`** - للتوثيق ⭐

#### السطر 73:
```env
WEB_SERVER_URL=https://1c547fe5.sendaddons.pages.dev  # ← غير هذا
```

## كيفية التحديث خطوة بخطوة 🔧

### الخطوة 1: تحديث الرابط الرئيسي
```python
# في ملف main.py - دالة get_web_server_url()
hosted_url = "https://YOUR-NEW-DOMAIN.pages.dev"
```

### الخطوة 2: تحديث رابط المعاينة
```python
# في ملف main.py - دالة user_preview_page()
preview_url = f"https://YOUR-NEW-DOMAIN.pages.dev/?preview=1&user_id={user_id}"
```

### الخطوة 3: تحديث روابط القوائم
```python
# في ملف main.py - دالة app_download_menu()
# استبدل جميع الروابط القديمة بالجديدة
```

### الخطوة 4: تحديث روابط الإرسال
```python
# في ملف main.py - دوال send_mod_to_channel() و send_mod_to_user()
base_web_server_url = "https://YOUR-NEW-DOMAIN.pages.dev"
```

### الخطوة 5: تحديث ملفات الإعدادات
```json
// في ملف app.json
"value": "https://YOUR-NEW-DOMAIN.pages.dev"
```

```env
# في ملف .env.example
WEB_SERVER_URL=https://YOUR-NEW-DOMAIN.pages.dev
```

## سكريبت التحديث التلقائي 🤖

يمكنك استخدام هذا السكريبت لتحديث جميع الروابط تلقائياً:

```python
# update_urls.py
import re

def update_all_urls(old_url, new_url):
    """تحديث جميع الروابط في الملفات"""
    
    files_to_update = [
        'main.py',
        'app.json',
        '.env.example',
        'QUICK_DEPLOY.md'
    ]
    
    for file_path in files_to_update:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال الروابط
            updated_content = content.replace(old_url, new_url)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"✅ تم تحديث {file_path}")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث {file_path}: {e}")

# الاستخدام
old_url = "https://1c547fe5.sendaddons.pages.dev"
new_url = "https://YOUR-NEW-DOMAIN.pages.dev"
update_all_urls(old_url, new_url)
```

## التحقق من التحديث ✅

بعد التحديث، تأكد من:

### 1. اختبار الروابط الجديدة
```bash
# اختبر الرابط الجديد في المتصفح
curl -I https://YOUR-NEW-DOMAIN.pages.dev
```

### 2. اختبار البوت
```bash
# شغل البوت واختبر:
# - إرسال مود لقناة
# - معاينة الصفحة
# - تحميل مود
```

### 3. فحص السجلات
```bash
# تأكد من عدم وجود أخطاء في السجلات
tail -f bot.log
```

## نصائح مهمة ⚠️

1. **احتفظ بنسخة احتياطية** من الملفات قبل التحديث
2. **اختبر الرابط الجديد** قبل التحديث
3. **تأكد من عمل HTTPS** على الرابط الجديد
4. **تحديث متغيرات البيئة** في الاستضافة
5. **إعادة تشغيل البوت** بعد التحديث

## استكشاف الأخطاء 🔍

### المشكلة: البوت لا يرسل روابط صحيحة
**الحل:** تأكد من تحديث جميع الأماكن المذكورة أعلاه

### المشكلة: صفحة المعاينة لا تعمل
**الحل:** تحقق من رابط المعاينة في دالة `user_preview_page()`

### المشكلة: الروابط القديمة ما زالت تظهر
**الحل:** امسح كاش البوت وأعد تشغيله

---

**ملاحظة:** تأكد من تحديث جميع الأماكن المذكورة لضمان عمل البوت بشكل صحيح مع الرابط الجديد.
