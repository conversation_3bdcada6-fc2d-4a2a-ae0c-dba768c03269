<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>اختبار الهاتف - Mobile Test</title>
    
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            width: 100vw;
            max-width: 100vw;
            overflow-x: hidden;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            width: 100vw;
            max-width: 100vw;
            padding: 10px;
            box-sizing: border-box;
        }
        
        .test-section {
            background: #2d2d2d;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            width: 100%;
        }
        
        .test-item {
            background: #444;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        
        .test-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .test-button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            width: 100%;
            margin: 5px 0;
            box-sizing: border-box;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.good {
            background: #4CAF50;
            color: white;
        }
        
        .status.bad {
            background: #f44336;
            color: white;
        }
        
        .status.warning {
            background: #ff9800;
            color: white;
        }
        
        @media (max-width: 480px) {
            .test-grid {
                grid-template-columns: 1fr;
                gap: 5px;
            }
            
            .test-container {
                padding: 5px;
            }
            
            .test-section {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 اختبار عرض الهاتف</h1>
        <p>هذه صفحة اختبار للتأكد من أن الصفحة تظهر بالحجم الصحيح على الهاتف</p>
        
        <div class="test-section">
            <h2>📊 معلومات الشاشة</h2>
            <div id="screen-info">
                <p><strong>عرض الشاشة:</strong> <span id="screen-width"></span>px</p>
                <p><strong>ارتفاع الشاشة:</strong> <span id="screen-height"></span>px</p>
                <p><strong>نوع الجهاز:</strong> <span id="device-type"></span></p>
                <p><strong>نسبة البكسل:</strong> <span id="pixel-ratio"></span></p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>✅ نتائج الاختبار</h2>
            <div id="test-results">
                <!-- سيتم ملء النتائج هنا -->
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔲 اختبار الشبكة</h2>
            <div class="test-grid">
                <div class="test-item">عنصر 1</div>
                <div class="test-item">عنصر 2</div>
                <div class="test-item">عنصر 3</div>
                <div class="test-item">عنصر 4</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ اختبار الصورة</h2>
            <div class="test-image">
                صورة تجريبية
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔘 اختبار الأزرار</h2>
            <button class="test-button" onclick="alert('الزر يعمل!')">اضغط هنا للاختبار</button>
            <button class="test-button" onclick="testScroll()">اختبار التمرير</button>
        </div>
        
        <div class="test-section">
            <h2>📝 نص طويل للاختبار</h2>
            <p>
                هذا نص طويل جداً للتأكد من أن النصوص لا تتجاوز حدود الشاشة ولا تسبب تمرير أفقي. 
                يجب أن يكون هذا النص قابلاً للقراءة بدون الحاجة للتمرير يميناً أو يساراً. 
                إذا كان بإمكانك قراءة هذا النص بالكامل بدون تمرير أفقي، فهذا يعني أن الاختبار نجح.
                النص العربي يجب أن يظهر بشكل صحيح من اليمين إلى اليسار بدون مشاكل في التخطيط.
            </p>
        </div>
    </div>
    
    <script>
        function updateScreenInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
            document.getElementById('screen-height').textContent = window.innerHeight;
            document.getElementById('pixel-ratio').textContent = window.devicePixelRatio || 1;
            
            let deviceType = 'سطح المكتب';
            if (window.innerWidth <= 480) {
                deviceType = 'هاتف صغير';
            } else if (window.innerWidth <= 768) {
                deviceType = 'هاتف/تابلت';
            } else if (window.innerWidth <= 1024) {
                deviceType = 'تابلت كبير';
            }
            
            document.getElementById('device-type').textContent = deviceType;
        }
        
        function runTests() {
            const results = [];
            
            // اختبار عدم وجود تمرير أفقي
            const bodyWidth = document.body.scrollWidth;
            const windowWidth = window.innerWidth;
            const hasHorizontalScroll = bodyWidth > windowWidth;
            
            results.push({
                name: 'عدم وجود تمرير أفقي',
                passed: !hasHorizontalScroll,
                details: `عرض المحتوى: ${bodyWidth}px، عرض النافذة: ${windowWidth}px`
            });
            
            // اختبار viewport
            const viewport = document.querySelector('meta[name="viewport"]');
            results.push({
                name: 'وجود viewport صحيح',
                passed: !!viewport && viewport.content.includes('width=device-width'),
                details: viewport ? viewport.content : 'غير موجود'
            });
            
            // اختبار حجم الحاوي
            const container = document.querySelector('.test-container');
            const containerWidth = container.offsetWidth;
            const containerFitsScreen = containerWidth <= windowWidth;
            
            results.push({
                name: 'الحاوي يناسب الشاشة',
                passed: containerFitsScreen,
                details: `عرض الحاوي: ${containerWidth}px`
            });
            
            // اختبار الشبكة
            const grid = document.querySelector('.test-grid');
            const gridStyle = window.getComputedStyle(grid);
            const expectedColumns = window.innerWidth <= 480 ? 1 : 2;
            const actualColumns = gridStyle.gridTemplateColumns.split(' ').length;
            
            results.push({
                name: 'الشبكة تتكيف مع الشاشة',
                passed: actualColumns === expectedColumns,
                details: `أعمدة فعلية: ${actualColumns}، متوقعة: ${expectedColumns}`
            });
            
            // عرض النتائج
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = results.map(result => {
                const statusClass = result.passed ? 'good' : 'bad';
                const statusText = result.passed ? '✅ نجح' : '❌ فشل';
                return `
                    <div class="status ${statusClass}">
                        <strong>${result.name}:</strong> ${statusText}<br>
                        <small>${result.details}</small>
                    </div>
                `;
            }).join('');
            
            // ملخص عام
            const passedTests = results.filter(r => r.passed).length;
            const totalTests = results.length;
            const overallStatus = passedTests === totalTests ? 'good' : (passedTests > 0 ? 'warning' : 'bad');
            const overallText = passedTests === totalTests ? '🎉 جميع الاختبارات نجحت!' : 
                               passedTests > 0 ? '⚠️ بعض الاختبارات فشلت' : '❌ جميع الاختبارات فشلت';
            
            resultsContainer.innerHTML += `
                <div class="status ${overallStatus}">
                    <strong>النتيجة النهائية:</strong> ${overallText}<br>
                    <small>${passedTests}/${totalTests} اختبارات نجحت</small>
                </div>
            `;
        }
        
        function testScroll() {
            const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
            if (hasHorizontalScroll) {
                alert('⚠️ يوجد تمرير أفقي! هناك مشكلة في التصميم.');
            } else {
                alert('✅ لا يوجد تمرير أفقي. التصميم صحيح!');
            }
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateScreenInfo();
            setTimeout(runTests, 500);
        });
        
        // تحديث عند تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            updateScreenInfo();
            setTimeout(runTests, 300);
        });
        
        // تحديث عند تغيير الاتجاه
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                updateScreenInfo();
                runTests();
            }, 500);
        });
    </script>
</body>
</html>
