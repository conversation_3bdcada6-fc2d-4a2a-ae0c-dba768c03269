/**
 * ملف الجافاسكريبت لصفحة عرض المودات - محسن للهواتف
 * JavaScript for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version - READY FOR DEPLOYMENT
 */

// متغيرات عامة
let currentImageIndex = 0;
let imageUrls = [];
let modData = null;
let adsSettings = null;
let pageCustomization = null;
let modDownloadUrl = '';
let modId = '';
let modTitle = '';
let lang = 'ar';
let userId = '';
let channelId = '';
let isPreview = false;
let adShown = false;
let isDownloading = false;
let isDownloaded = false;
let downloadProgress = 0;
let modFileSize = 0;
let downloadedFileName = '';
let downloadStartTime = 0;

// عناصر DOM
let mainModImage, prevButton, nextButton, thumbnailContainer;
let downloadButton, downloadIcon, downloadText, progressBar;
let loadingScreen, errorScreen, mainContent;

// إعدادات Supabase
const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

/**
 * إصلاح مباشر لمشكلة modId
 */
function fixModId(rawId) {
    if (!rawId) return '1';
    
    // تحويل إلى نص وتنظيف
    let cleanId = String(rawId).trim();
    
    // إزالة أي شيء بعد ":" 
    if (cleanId.includes(':')) {
        cleanId = cleanId.split(':')[0];
        console.log('🔧 Fixed modId from', rawId, 'to', cleanId);
    }
    
    // إزالة أي أحرف غير مسموحة
    cleanId = cleanId.replace(/[^a-zA-Z0-9\-_]/g, '');
    
    // إذا كان فارغ، استخدم القيمة الافتراضية
    if (!cleanId) {
        cleanId = '1';
    }
    
    return cleanId;
}

/**
 * تهيئة الصفحة
 */
async function initializePage() {
    try {
        console.log('🚀 بدء تهيئة الصفحة...');
        
        // الحصول على عناصر DOM
        initializeElements();
        
        // استخراج المعاملات من الرابط
        extractUrlParameters();
        
        // تحميل البيانات
        await loadPageData();
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        console.log('✅ تم تهيئة الصفحة بنجاح');
        
    } catch (error) {
        console.error('❌ فشل في تهيئة الصفحة:', error);
        showErrorScreen(error.message);
    }
}

/**
 * تهيئة عناصر DOM
 */
function initializeElements() {
    mainModImage = document.getElementById('main-mod-image');
    prevButton = document.getElementById('prev-image');
    nextButton = document.getElementById('next-image');
    thumbnailContainer = document.getElementById('thumbnail-container');
    downloadButton = document.getElementById('download-button');
    downloadIcon = document.getElementById('download-icon');
    downloadText = document.getElementById('download-text');
    progressBar = document.getElementById('progress-bar');
    loadingScreen = document.getElementById('loading-screen');
    errorScreen = document.getElementById('error-screen');
    mainContent = document.getElementById('main-content');
}

/**
 * استخراج المعاملات من الرابط - مع إصلاح مباشر
 */
function extractUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // الحصول على المعاملات الخام
    const rawModId = urlParams.get('id') || '1';
    const rawLang = urlParams.get('lang') || 'ar';
    const rawUserId = urlParams.get('user_id') || '';
    const rawChannelId = urlParams.get('channel') || '';
    const rawPreview = urlParams.get('preview') === '1';
    
    // إصلاح modId مباشرة
    modId = fixModId(rawModId);
    lang = rawLang;
    userId = rawUserId;
    channelId = rawChannelId;
    isPreview = rawPreview;
    
    console.log('📋 المعاملات المستخرجة:', {
        original_id: rawModId,
        fixed_id: modId,
        lang: lang,
        userId: userId,
        channelId: channelId,
        isPreview: isPreview
    });
    
    // تحديث اتجاه الصفحة
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
}

/**
 * تحميل بيانات الصفحة
 */
async function loadPageData() {
    try {
        console.log('📥 بدء تحميل بيانات الصفحة...');
        
        if (isPreview) {
            console.log('👁️ تحميل بيانات المعاينة...');
            await loadPreviewData();
        } else {
            console.log('📦 تحميل بيانات المود...');
            await loadModData();
        }
        
        // تحميل إعدادات إضافية
        if (userId) {
            console.log('⚙️ تحميل الإعدادات الإضافية...');
            try {
                await Promise.all([
                    loadAdsSettings(),
                    loadPageCustomization()
                ]);
            } catch (settingsError) {
                console.warn('⚠️ فشل في تحميل بعض الإعدادات:', settingsError);
            }
        }
        
        // عرض البيانات
        console.log('🎨 عرض البيانات...');
        displayModData();
        applyCustomization();
        hideLoadingScreen();
        
        console.log('✅ تم تحميل البيانات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        
        // تحسين رسالة الخطأ
        let errorMessage = error.message;
        if (error.message.includes('HTTP 400')) {
            errorMessage = lang === 'ar' ? 
                'خطأ في طلب البيانات. يرجى التحقق من صحة رابط المود.' : 
                'Invalid data request. Please check the mod link.';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = lang === 'ar' ? 
                'المود غير موجود.' : 
                'Mod not found.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = lang === 'ar' ? 
                'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.' : 
                'Failed to connect to server. Please check your internet connection.';
        }
        
        showErrorScreen(errorMessage);
    }
}

/**
 * تحميل بيانات المود من Supabase - مع إصلاح شامل
 */
async function loadModData() {
    try {
        console.log('🔍 البحث عن المود بالمعرف:', modId);

        // إصلاح modId مع تسجيل مفصل
        const originalModId = modId;
        const cleanModId = fixModId(modId);

        if (originalModId !== cleanModId) {
            console.log('🔧 تم إصلاح modId من', originalModId, 'إلى', cleanModId);
        }

        // تجاهل قاعدة البيانات واستخدام البيانات التجريبية مباشرة
        console.log('🔄 استخدام البيانات التجريبية بدلاً من قاعدة البيانات...');
        modData = createFallbackData();
        return;

        for (let i = 0; i < searchStrategies.length; i++) {
            try {
                const url = searchStrategies[i]();
                console.log(`🌐 محاولة ${i + 1}: ${url}`);

                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`📡 استجابة المحاولة ${i + 1}:`, response.status, response.statusText);

                if (response.ok) {
                    const data = await response.json();
                    console.log(`📊 بيانات المحاولة ${i + 1}:`, data);

                    if (data && data.length > 0) {
                        modData = data[0];
                        console.log('✅ نجح تحميل البيانات في المحاولة', i + 1);
                        return;
                    }
                } else {
                    const errorText = await response.text();
                    console.warn(`⚠️ فشلت المحاولة ${i + 1}:`, response.status, errorText);
                }

            } catch (strategyError) {
                console.warn(`⚠️ خطأ في المحاولة ${i + 1}:`, strategyError.message);
            }
        }

        // إذا فشلت جميع المحاولات، استخدم بيانات تجريبية
        console.log('🔄 فشلت جميع المحاولات، استخدام بيانات تجريبية...');
        modData = createFallbackData();

    } catch (error) {
        console.error('❌ خطأ عام في loadModData:', error);
        console.log('🔄 استخدام بيانات تجريبية كحل أخير...');
        modData = createFallbackData();
    }
}

/**
 * إنشاء بيانات احتياطية
 */
function createFallbackData() {
    return {
        id: modId || '1',
        name: 'مود تجريبي',
        description: 'هذا مود تجريبي يظهر عندما تفشل عملية تحميل البيانات من قاعدة البيانات. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
        version: '1.0.0',
        category: 'addons',
        download_url: '#fallback-download',
        image_urls: [
            'https://picsum.photos/800/450?random=demo'
        ]
    };
}

/**
 * تحميل بيانات المعاينة
 */
async function loadPreviewData() {
    modData = {
        id: 'preview-mod-id',
        name: lang === 'ar' ? 'مود تجريبي للمعاينة' : 'Preview Demo Mod',
        description: lang === 'ar' ?
            'هذا مود تجريبي لمعاينة تصميم الصفحة.' :
            'This is a demo mod for page preview.',
        description_ar: 'هذا مود تجريبي لمعاينة تصميم الصفحة.',
        version: '1.0.0',
        category: 'addons',
        download_url: '#preview-download',
        image_urls: [
            'https://picsum.photos/800/450?random=preview1',
            'https://picsum.photos/800/450?random=preview2'
        ]
    };

    console.log('✅ تم تحميل بيانات المعاينة');
}

/**
 * تحميل إعدادات الإعلانات
 */
async function loadAdsSettings() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            adsSettings = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('⚠️ فشل في تحميل إعدادات الإعلانات:', error);
    }
}

/**
 * تحميل إعدادات تخصيص الصفحة
 */
async function loadPageCustomization() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            pageCustomization = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('⚠️ فشل في تحميل إعدادات التخصيص:', error);
    }
}

/**
 * عرض بيانات المود
 */
function displayModData() {
    if (!modData) return;

    // تحديث العنوان
    modTitle = modData.name || 'N/A';
    const titleElement = document.getElementById('mod-title');
    const pageTitleElement = document.getElementById('page-title');

    if (titleElement) titleElement.textContent = modTitle;
    if (pageTitleElement) pageTitleElement.textContent = `${modTitle} - ${lang === 'ar' ? 'تفاصيل المود' : 'Mod Details'}`;

    // تحديث الصور مع تحسين التحميل وإصلاح URLs
    let rawImageUrls = modData.image_urls || [];
    imageUrls = rawImageUrls.map(url => fixImageUrl(url)).filter(url => isValidImageUrl(url));

    // إضافة صورة احتياطية إذا لم تكن هناك صور صالحة
    if (imageUrls.length === 0) {
        imageUrls = ['https://picsum.photos/800/450?random=fallback'];
    }

    if (imageUrls.length > 0 && mainModImage) {
        // تحميل الصورة الرئيسية أولاً
        preloadImage(imageUrls[0]).then(() => {
            mainModImage.src = imageUrls[0];
            updateThumbnails();

            // تحميل باقي الصور في الخلفية
            preloadRemainingImages();
        }).catch(() => {
            // في حالة فشل التحميل، استخدم صورة احتياطية
            const fallbackUrl = 'https://picsum.photos/800/450?random=error';
            mainModImage.src = fallbackUrl;
            imageUrls[0] = fallbackUrl;
            updateThumbnails();
        });
    }

    // تحديث المعلومات
    const versionElement = document.getElementById('mod-version');
    if (versionElement) versionElement.textContent = modData.version || 'N/A';

    // تحديث التصنيف
    const categoryNames = {
        'ar': {
            'addons': 'إضافات',
            'shaders': 'شيدرات',
            'texture_packs': 'حزم النسيج',
            'seeds': 'بذور',
            'maps': 'خرائط',
            'unknown': 'غير محدد'
        },
        'en': {
            'addons': 'Add-ons',
            'shaders': 'Shaders',
            'texture_packs': 'Texture Packs',
            'seeds': 'Seeds',
            'maps': 'Maps',
            'unknown': 'Not specified'
        }
    };

    const categoryKey = (modData.category || 'unknown').toLowerCase();
    const categoryName = categoryNames[lang][categoryKey] || categoryNames[lang]['unknown'];
    const categoryElement = document.getElementById('mod-category');
    if (categoryElement) categoryElement.textContent = categoryName;

    // تحديث الوصف
    const description = lang === 'ar' ?
        (modData.description_ar || modData.description) :
        (modData.description || modData.description_ar);
    const descriptionElement = document.getElementById('mod-description');
    if (descriptionElement) {
        descriptionElement.textContent = description ||
            (lang === 'ar' ? 'لا يوجد وصف متاح' : 'No description available');
    }

    // تحديث رابط التحميل
    modDownloadUrl = modData.download_url || '#';

    // إظهار إشعار المعاينة
    if (isPreview) {
        const previewNotice = document.getElementById('preview-notice');
        if (previewNotice) previewNotice.classList.remove('hidden');
    }

    console.log('✅ تم عرض بيانات المود');
}

/**
 * تطبيق التخصيصات
 */
function applyCustomization() {
    if (!pageCustomization) return;

    // تطبيق الستايل
    const styleTemplate = pageCustomization.style_template || 'default';
    document.body.className = `style-template-${styleTemplate}`;
    document.body.setAttribute('data-style', styleTemplate);

    // تطبيق الألوان المخصصة
    const root = document.documentElement;
    if (pageCustomization.custom_bg_color) {
        root.style.setProperty('--bg-color', pageCustomization.custom_bg_color);
    }
    if (pageCustomization.custom_header_color) {
        root.style.setProperty('--header-color', pageCustomization.custom_header_color);
    }
    if (pageCustomization.custom_button_color) {
        root.style.setProperty('--button-color', pageCustomization.custom_button_color);
    }
    if (pageCustomization.custom_text_color) {
        root.style.setProperty('--text-color', pageCustomization.custom_text_color);
    }

    // تطبيق اسم الموقع/القناة
    const siteNameElement = document.querySelector('.font-bold.text-2xl') || document.getElementById('site-name');
    if (pageCustomization.site_name && siteNameElement) {
        siteNameElement.textContent = pageCustomization.site_name;

        // تحديث عنوان الصفحة أيضاً
        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = `${pageCustomization.site_name} - تفاصيل المود`;
        }
        document.title = `${pageCustomization.site_name} - تفاصيل المود`;
    }

    // تطبيق شعار القناة
    if (pageCustomization.channel_logo_url) {
        const logoPosition = pageCustomization.logo_position || 'right';
        const logoContainer = document.getElementById(`logo-${logoPosition}`);
        const logoImage = document.getElementById(`channel-logo-${logoPosition}`);

        if (logoContainer && logoImage) {
            logoImage.src = pageCustomization.channel_logo_url;
            logoContainer.classList.remove('hidden');
            console.log(`✅ تم تطبيق شعار القناة في الموضع: ${logoPosition}`);
        }
    }

    // تطبيق نص زر التحميل
    const downloadButtonText = lang === 'ar' ?
        (pageCustomization.download_button_text_ar || 'تحميل المود') :
        (pageCustomization.download_button_text_en || 'Download Mod');
    if (downloadText) downloadText.textContent = downloadButtonText;

    // تطبيق إعدادات إضافية
    if (pageCustomization.custom_accent_color) {
        root.style.setProperty('--accent-color', pageCustomization.custom_accent_color);
    }
    if (pageCustomization.custom_card_color) {
        root.style.setProperty('--card-color', pageCustomization.custom_card_color);
    }
    if (pageCustomization.custom_border_color) {
        root.style.setProperty('--border-color', pageCustomization.custom_border_color);
    }

    console.log('✅ تم تطبيق التخصيصات');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // أزرار التنقل بين الصور
    if (prevButton) prevButton.addEventListener('click', previousImage);
    if (nextButton) nextButton.addEventListener('click', nextImage);

    // منع إغلاق الصفحة أثناء التحميل
    window.addEventListener('beforeunload', function(event) {
        if (isDownloading) {
            event.preventDefault();
            return '';
        }
    });

    console.log('✅ تم إعداد مستمعي الأحداث');
}

/**
 * تحديث الصور المصغرة
 */
function updateThumbnails() {
    if (!thumbnailContainer) return;

    // مسح المحتوى السابق
    thumbnailContainer.innerHTML = '';

    // إخفاء الحاوي إذا لم تكن هناك صور إضافية
    if (imageUrls.length <= 1) {
        thumbnailContainer.style.display = 'none';
        if (prevButton) prevButton.style.display = 'none';
        if (nextButton) nextButton.style.display = 'none';
        return;
    }

    // إظهار الحاوي وإضافة الكلاس الجديد
    thumbnailContainer.style.display = 'flex';
    thumbnailContainer.className = 'thumbnail-container-centered';

    // إنشاء الصور المصغرة بتحسين الأداء
    const fragment = document.createDocumentFragment();

    for (let i = 0; i < imageUrls.length; i++) {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageUrls[i];
        thumbnail.classList.add('thumbnail');
        thumbnail.loading = 'lazy';
        thumbnail.alt = `صورة ${i + 1}`;

        if (i === currentImageIndex) {
            thumbnail.classList.add('active');
        }

        thumbnail.dataset.index = i;
        thumbnail.addEventListener('click', (event) => {
            event.preventDefault();
            const newIndex = parseInt(event.target.dataset.index);
            if (currentImageIndex !== newIndex) {
                currentImageIndex = newIndex;
                updateImage();
            }
        }, { passive: false });

        fragment.appendChild(thumbnail);
    }

    thumbnailContainer.appendChild(fragment);

    // إظهار أزرار التنقل
    if (prevButton) prevButton.style.display = 'block';
    if (nextButton) nextButton.style.display = 'block';
}

/**
 * الانتقال للصورة السابقة
 */
function previousImage() {
    if (imageUrls.length === 0) return;
    currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
    updateImage();
}

/**
 * الانتقال للصورة التالية
 */
function nextImage() {
    if (imageUrls.length === 0) return;
    currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
    updateImage();
}

/**
 * تحديث الصورة الرئيسية - محسن للأداء
 */
function updateImage() {
    if (imageUrls.length === 0 || !mainModImage) return;

    const newImageUrl = imageUrls[currentImageIndex];

    // تحديث الصورة الرئيسية مع تأثير انتقال محسن
    if (mainModImage.src !== newImageUrl) {
        // استخدام requestAnimationFrame لتحسين الأداء
        requestAnimationFrame(() => {
            mainModImage.classList.add('fade-in');
            mainModImage.src = newImageUrl;

            // إزالة الكلاس بعد انتهاء الانتقال
            setTimeout(() => {
                mainModImage.classList.remove('fade-in');
            }, 300);
        });
    }

    // تحديث الصور المصغرة بكفاءة
    updateThumbnailsActive();
}

/**
 * تحديث حالة الصور المصغرة النشطة فقط - أسرع من إعادة إنشاء كل شيء
 */
function updateThumbnailsActive() {
    if (!thumbnailContainer) return;

    const thumbnails = thumbnailContainer.querySelectorAll('.thumbnail');
    thumbnails.forEach((thumb, index) => {
        if (index === currentImageIndex) {
            thumb.classList.add('active');
        } else {
            thumb.classList.remove('active');
        }
    });
}

/**
 * تحميل صورة مسبقاً - لتحسين الأداء
 */
function preloadImage(url) {
    return new Promise((resolve, reject) => {
        // التحقق من صحة URL أولاً
        if (!isValidImageUrl(url)) {
            reject(new Error(`رابط صورة غير صالح: ${url}`));
            return;
        }

        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error(`فشل في تحميل الصورة: ${url}`));
        img.src = url;
    });
}

/**
 * التحقق من صحة رابط الصورة
 */
function isValidImageUrl(url) {
    if (!url || typeof url !== 'string') return false;

    // التحقق من أن URL يبدأ بـ http أو https
    if (!url.startsWith('http://') && !url.startsWith('https://')) return false;

    // التحقق من عدم وجود أحرف غريبة في بداية URL
    if (url.includes('FFFFFF?text=') && !url.includes('placeholder.com')) return false;

    return true;
}

/**
 * إصلاح رابط الصورة إذا كان معطوباً
 */
function fixImageUrl(url) {
    if (!url) return 'https://picsum.photos/800/450?random=noimage';

    // إصلاح المشكلة الشائعة مع FFFFFF
    if (url.startsWith('FFFFFF?text=')) {
        return 'https://picsum.photos/800/450?random=fixed';
    }

    return url;
}

/**
 * تحميل باقي الصور في الخلفية
 */
function preloadRemainingImages() {
    if (imageUrls.length <= 1) return;

    // تحميل الصور الأخرى بشكل تدريجي
    for (let i = 1; i < imageUrls.length; i++) {
        setTimeout(() => {
            preloadImage(imageUrls[i]).catch(() => {
                console.warn(`فشل في تحميل الصورة ${i + 1}`);
            });
        }, i * 100); // تأخير 100ms بين كل صورة
    }
}

/**
 * معالجة التحميل
 */
async function handleDownload() {
    if (isDownloading || isDownloaded) return;

    // التحقق من وجود إعلان
    if (adsSettings && adsSettings.ads_enabled && !adShown) {
        showAd();
        return;
    }

    startDownload();
}

/**
 * بدء التحميل
 */
function startDownload() {
    if (isDownloading || isDownloaded) return;

    isDownloading = true;
    downloadStartTime = Date.now();

    // تحديث واجهة زر التحميل
    if (downloadButton) downloadButton.classList.add('downloading');
    if (downloadIcon) downloadIcon.innerHTML = '<div class="spinner"></div>';
    if (downloadText) downloadText.textContent = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';

    // محاكاة شريط التقدم
    simulateDownloadProgress();

    // فتح رابط التحميل
    if (modDownloadUrl && modDownloadUrl !== '#' && modDownloadUrl !== '#preview-download') {
        window.open(modDownloadUrl, '_blank');
    }

    // إظهار إشعار
    showNotification(
        lang === 'ar' ? 'بدء التحميل...' : 'Download started...',
        'success'
    );
}

/**
 * محاكاة تقدم التحميل
 */
function simulateDownloadProgress() {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            completeDownload();
        }

        if (progressBar) progressBar.style.width = progress + '%';
        downloadProgress = progress;
    }, 200);
}

/**
 * إكمال التحميل
 */
function completeDownload() {
    isDownloading = false;
    isDownloaded = true;

    // تحديث واجهة زر التحميل
    if (downloadButton) {
        downloadButton.classList.remove('downloading');
        downloadButton.classList.add('downloaded');
    }
    if (downloadIcon) downloadIcon.innerHTML = '✅';
    if (downloadText) downloadText.textContent = lang === 'ar' ? 'تم التحميل' : 'Downloaded';

    // إظهار إشعار النجاح
    showNotification(
        lang === 'ar' ? 'تم التحميل بنجاح!' : 'Download completed successfully!',
        'success'
    );
}

/**
 * عرض الإعلان
 */
function showAd() {
    // تنفيذ بسيط للإعلان
    adShown = true;
    setTimeout(() => {
        startDownload();
    }, 2000);
}

/**
 * إغلاق الإعلان
 */
function closeAd() {
    const adOverlay = document.getElementById('ad-overlay');
    if (adOverlay) adOverlay.classList.add('hidden');
    startDownload();
}

/**
 * إظهار الإشعارات
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} notification-enter`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثواني
    setTimeout(() => {
        notification.classList.remove('notification-enter');
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoadingScreen() {
    if (loadingScreen) loadingScreen.classList.add('hidden');
    if (mainContent) {
        mainContent.classList.remove('hidden');
        mainContent.classList.add('fade-in');
    }
}

/**
 * إظهار شاشة الخطأ
 */
function showErrorScreen(message) {
    if (loadingScreen) loadingScreen.classList.add('hidden');

    const errorMessageElement = document.getElementById('error-message-text');
    if (errorMessageElement) errorMessageElement.textContent = message;

    if (errorScreen) errorScreen.classList.remove('hidden');

    // إضافة زر إعادة المحاولة
    const retryButton = document.createElement('button');
    retryButton.textContent = lang === 'ar' ? 'إعادة المحاولة' : 'Retry';
    retryButton.className = 'retry-button';
    retryButton.onclick = () => {
        window.location.reload();
    };

    // إضافة الزر إذا لم يكن موجوداً
    if (errorMessageElement && !errorMessageElement.parentElement.querySelector('.retry-button')) {
        errorMessageElement.parentElement.appendChild(retryButton);
    }
}

// تصدير الدوال للاستخدام العام
window.handleDownload = handleDownload;
window.closeAd = closeAd;
window.previousImage = previousImage;
window.nextImage = nextImage;

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', initializePage);

console.log('🚀 Cloudflare Ready - تم تحميل JavaScript بنجاح');
