# دليل نشر الصفحة المحدثة 🚀

## التحديثات الجديدة ✨

تم تطبيق التحديثات التالية على صفحة عرض المود:

### 1. **تصميم جديد مستوحى من ألعاب البكسل** 🎮
- تطبيق نفس تصميم صفحة `mod_feudal-furniture_ffea32ef.html`
- إضافة تأثيرات متوهجة للأزرار
- تأثيرات الجزيئات المتحركة
- حدود البكسل المميزة

### 2. **ميزة تحديث اسم القناة تلقائياً** 📺
- عند ربط قناة جديدة، يحصل البوت على اسم القناة من تيليجرام
- يتم تحديث عنوان الصفحة تلقائياً ليصبح اسم القناة بدلاً من "Modetaris"
- يتم حفظ شعار القناة إذا كان متوفراً

### 3. **ميزة تعديل عنوان الصفحة يدوياً** ✏️
- إضافة زر "تعديل اسم الموقع" في قائمة تخصيص الصفحة
- يمكن للمستخدم تغيير العنوان إلى أي اسم يريده
- التحديث فوري ويظهر في الصفحة مباشرة

## كيفية نشر الصفحة المحدثة 📤

### الخطوة 1: رفع الملفات
1. ارفع جميع ملفات مجلد `cloudflare_ready` إلى استضافة Cloudflare Pages
2. تأكد من رفع الملفات التالية:
   - `index.html` (محدث)
   - `style.css` (محدث)
   - `script.js` (محدث)
   - `style-templates.css`
   - جميع الملفات الأخرى

### الخطوة 2: استبدال الرابط في البوت
بعد نشر الصفحة الجديدة، تحتاج إلى تحديث الرابط في ملف `web_server.py`:

```python
# ابحث عن هذا السطر في ملف web_server.py
CLOUDFLARE_BASE_URL = "https://your-old-domain.pages.dev"

# واستبدله بالرابط الجديد
CLOUDFLARE_BASE_URL = "https://your-new-domain.pages.dev"
```

### الخطوة 3: إعادة تشغيل البوت
```bash
# أوقف البوت الحالي
# ثم شغله مرة أخرى
python main.py
```

## المتغيرات المطلوب تحديثها 🔧

### في ملف `web_server.py`:
```python
# الرابط الأساسي للصفحة
CLOUDFLARE_BASE_URL = "https://your-domain.pages.dev"

# أو إذا كان في ملف إعدادات منفصل
BASE_URL = "https://your-domain.pages.dev"
```

### في ملف `config.js` (إذا كان موجوداً):
```javascript
const CONFIG = {
    baseUrl: "https://your-domain.pages.dev",
    // باقي الإعدادات...
};
```

## اختبار الصفحة الجديدة 🧪

### 1. اختبار التصميم الجديد
- تأكد من ظهور التأثيرات المتوهجة للأزرار
- تحقق من عمل تأثيرات الجزيئات
- تأكد من ظهور حدود البكسل

### 2. اختبار ميزة اسم القناة
- اربط قناة جديدة بالبوت
- تحقق من تحديث اسم الصفحة تلقائياً
- تأكد من ظهور شعار القناة إذا كان متوفراً

### 3. اختبار التعديل اليدوي
- اذهب إلى: إعدادات القناة → تخصيص صفحة المود → تعديل اسم الموقع
- أدخل اسماً جديداً
- تحقق من التحديث في الصفحة

## الملفات المحدثة 📁

### `index.html`
- إضافة مكتبات Tailwind CSS و Press Start 2P
- تحديث هيكل HTML ليطابق تصميم صفحة المود
- إضافة عناصر تأثيرات الجزيئات

### `style.css`
- إضافة ستايلات البكسل الجديدة
- تأثيرات الأزرار المتوهجة
- تأثيرات الجزيئات المتحركة
- تحديث متغيرات الألوان

### `script.js`
- تحديث دالة `applyCustomization()`
- إضافة دعم شعار القناة
- تحديث عنوان الصفحة تلقائياً
- دعم الألوان الإضافية

### `main.py`
- إضافة دالة `update_channel_info_in_customization()`
- إضافة دالة `user_edit_site_name()`
- إضافة دالة `handle_site_name_input()`
- تحديث معالجات الأزرار

### `supabase_client.py`
- إضافة دالة `update_channel_info_in_customization()`
- تحديث دالة `update_user_page_customization()`

## نصائح مهمة ⚠️

1. **احتفظ بنسخة احتياطية** من الملفات القديمة قبل التحديث
2. **اختبر الصفحة** في بيئة تطوير قبل النشر النهائي
3. **تأكد من عمل جميع الروابط** بعد التحديث
4. **راقب سجلات الأخطاء** بعد النشر للتأكد من عدم وجود مشاكل

## الدعم الفني 🆘

إذا واجهت أي مشاكل:
1. تحقق من سجلات البوت للأخطاء
2. تأكد من صحة الروابط المحدثة
3. تحقق من إعدادات قاعدة البيانات
4. تأكد من عمل جميع المتغيرات البيئية

---

**ملاحظة:** هذا التحديث يحافظ على جميع الوظائف الحالية ويضيف ميزات جديدة فقط. لن تفقد أي بيانات أو إعدادات موجودة.
