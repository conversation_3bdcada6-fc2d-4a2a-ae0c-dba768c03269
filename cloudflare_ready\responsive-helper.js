/**
 * مساعد التجاوب - يضمن عمل التصميم المتجاوب على جميع الأجهزة
 * Responsive Helper - Ensures responsive design works on all devices
 */

(function() {
    'use strict';
    
    // إصلاح viewport للهواتف
    function fixViewport() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
    }
    
    // إصلاح مشاكل الفيض الأفقي
    function fixOverflow() {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.overflowX === 'visible') {
                element.style.overflowX = 'hidden';
            }
        });
    }
    
    // إصلاح أحجام الصور
    function fixImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.style.display = 'block';
        });
    }
    
    // إصلاح الحاويات
    function fixContainers() {
        const containers = document.querySelectorAll('.container, .grid, .flex');
        containers.forEach(container => {
            container.style.maxWidth = '100%';
            container.style.boxSizing = 'border-box';
        });
    }
    
    // إصلاح النصوص الطويلة
    function fixTextOverflow() {
        const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
        textElements.forEach(element => {
            element.style.wordWrap = 'break-word';
            element.style.overflowWrap = 'break-word';
            element.style.maxWidth = '100%';
        });
    }
    
    // إصلاح الشبكة للهواتف
    function fixGridForMobile() {
        if (window.innerWidth <= 768) {
            const grids = document.querySelectorAll('.grid-cols-2');
            grids.forEach(grid => {
                if (window.innerWidth <= 480) {
                    grid.style.gridTemplateColumns = '1fr';
                } else {
                    grid.style.gridTemplateColumns = '1fr 1fr';
                }
                grid.style.gap = '0.5rem';
                grid.style.width = '100%';
            });
        }
    }
    
    // إصلاح الأزرار
    function fixButtons() {
        const buttons = document.querySelectorAll('button, .pixel-button, .download-button');
        buttons.forEach(button => {
            button.style.maxWidth = '100%';
            button.style.boxSizing = 'border-box';
            
            if (window.innerWidth <= 768) {
                button.style.width = '100%';
            }
        });
    }
    
    // مراقبة تغيير حجم الشاشة
    function handleResize() {
        fixGridForMobile();
        fixButtons();
        fixContainers();
        
        // إضافة فئة CSS حسب حجم الشاشة
        const body = document.body;
        body.classList.remove('mobile', 'tablet', 'desktop');
        
        if (window.innerWidth <= 480) {
            body.classList.add('mobile');
        } else if (window.innerWidth <= 768) {
            body.classList.add('tablet');
        } else {
            body.classList.add('desktop');
        }
    }
    
    // إصلاح مشكلة الزوم في iOS
    function fixIOSZoom() {
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.style.fontSize = '16px';
        });
    }
    
    // إصلاح التمرير السلس
    function fixSmoothScrolling() {
        document.documentElement.style.scrollBehavior = 'smooth';
        document.body.style.webkitOverflowScrolling = 'touch';
    }
    
    // تطبيق جميع الإصلاحات
    function applyAllFixes() {
        fixViewport();
        fixOverflow();
        fixImages();
        fixContainers();
        fixTextOverflow();
        fixGridForMobile();
        fixButtons();
        fixIOSZoom();
        fixSmoothScrolling();
        handleResize();
    }
    
    // تشغيل الإصلاحات عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyAllFixes);
    } else {
        applyAllFixes();
    }
    
    // تشغيل الإصلاحات عند تغيير حجم الشاشة
    window.addEventListener('resize', handleResize);
    
    // تشغيل الإصلاحات عند تغيير الاتجاه
    window.addEventListener('orientationchange', function() {
        setTimeout(applyAllFixes, 100);
    });
    
    // إصلاح إضافي للتأكد من عمل التجاوب
    setTimeout(function() {
        applyAllFixes();
        
        // إضافة فئة للتأكد من تحميل الإصلاحات
        document.body.classList.add('responsive-fixes-loaded');
        
        console.log('✅ تم تطبيق إصلاحات التجاوب بنجاح');
    }, 500);
    
    // مراقبة إضافة عناصر جديدة
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    setTimeout(function() {
                        fixImages();
                        fixContainers();
                        fixTextOverflow();
                        fixButtons();
                    }, 100);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // إضافة CSS إضافي عبر JavaScript
    const additionalCSS = `
        <style>
        /* إصلاحات CSS إضافية عبر JavaScript */
        * { box-sizing: border-box !important; }
        html, body { overflow-x: hidden !important; max-width: 100vw !important; }
        .container { width: 100% !important; max-width: 100% !important; }
        img { max-width: 100% !important; height: auto !important; }
        
        @media (max-width: 768px) {
            .grid-cols-2 { grid-template-columns: 1fr 1fr !important; }
            .container { padding: 0.5rem !important; }
        }
        
        @media (max-width: 480px) {
            .grid-cols-2 { grid-template-columns: 1fr !important; }
            .container { padding: 0.25rem !important; }
        }
        </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', additionalCSS);
    
})();
