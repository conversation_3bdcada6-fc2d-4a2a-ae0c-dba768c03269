<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>اختبار بسيط - Simple Test</title>
    
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            width: 100vw;
            max-width: 100vw;
            overflow-x: hidden;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            font-size: 16px;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
            padding: 10px;
            box-sizing: border-box;
        }
        
        .section {
            background: #2d2d2d;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            width: 100%;
        }
        
        .grid-item {
            background: #444;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        
        .image-test {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .button-test {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            width: 100%;
            margin: 5px 0;
            box-sizing: border-box;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .good { background: #4CAF50; }
        .bad { background: #f44336; }
        .warning { background: #ff9800; }
        
        h1 { font-size: 24px; margin: 10px 0; }
        h2 { font-size: 18px; margin: 10px 0; }
        
        @media (max-width: 480px) {
            .grid {
                grid-template-columns: 1fr;
                gap: 5px;
            }
            
            .container {
                padding: 5px;
            }
            
            .section {
                padding: 10px;
            }
            
            h1 { font-size: 20px; }
            h2 { font-size: 16px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 اختبار بسيط للهاتف</h1>
        <p>صفحة اختبار بسيطة للتأكد من عمل التصميم المتجاوب</p>
        
        <div class="section">
            <h2>📊 معلومات الشاشة</h2>
            <div id="info">
                <p><strong>العرض:</strong> <span id="width"></span>px</p>
                <p><strong>الارتفاع:</strong> <span id="height"></span>px</p>
                <p><strong>النوع:</strong> <span id="type"></span></p>
            </div>
        </div>
        
        <div class="section">
            <h2>✅ حالة الاختبار</h2>
            <div id="status"></div>
        </div>
        
        <div class="section">
            <h2>🔲 شبكة تجريبية</h2>
            <div class="grid">
                <div class="grid-item">1</div>
                <div class="grid-item">2</div>
                <div class="grid-item">3</div>
                <div class="grid-item">4</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🖼️ صورة تجريبية</h2>
            <div class="image-test">صورة متجاوبة</div>
        </div>
        
        <div class="section">
            <h2>🔘 زر تجريبي</h2>
            <button class="button-test" onclick="testFunction()">اضغط للاختبار</button>
        </div>
        
        <div class="section">
            <h2>📝 نص طويل</h2>
            <p>
                هذا نص طويل للتأكد من أن النصوص لا تتجاوز حدود الشاشة. 
                يجب أن يكون هذا النص قابلاً للقراءة بدون تمرير أفقي. 
                إذا كان بإمكانك قراءة هذا النص بالكامل بدون مشاكل، فهذا يعني أن التصميم يعمل بشكل صحيح.
            </p>
        </div>
    </div>
    
    <script>
        function updateInfo() {
            document.getElementById('width').textContent = window.innerWidth;
            document.getElementById('height').textContent = window.innerHeight;
            
            let type = 'سطح المكتب';
            if (window.innerWidth <= 480) {
                type = 'هاتف صغير';
            } else if (window.innerWidth <= 768) {
                type = 'هاتف/تابلت';
            }
            
            document.getElementById('type').textContent = type;
        }
        
        function checkStatus() {
            const bodyWidth = document.body.scrollWidth;
            const windowWidth = window.innerWidth;
            const hasScroll = bodyWidth > windowWidth;
            
            const statusDiv = document.getElementById('status');
            
            if (hasScroll) {
                statusDiv.innerHTML = '<div class="status bad">❌ يوجد تمرير أفقي</div>';
            } else {
                statusDiv.innerHTML = '<div class="status good">✅ لا يوجد تمرير أفقي</div>';
            }
            
            // إضافة معلومات إضافية
            statusDiv.innerHTML += `
                <div class="status warning">
                    <small>عرض المحتوى: ${bodyWidth}px | عرض النافذة: ${windowWidth}px</small>
                </div>
            `;
        }
        
        function testFunction() {
            alert('✅ الزر يعمل بشكل صحيح!');
        }
        
        function runAllTests() {
            updateInfo();
            checkStatus();
        }
        
        // تشغيل الاختبارات
        window.addEventListener('load', runAllTests);
        window.addEventListener('resize', runAllTests);
        window.addEventListener('orientationchange', function() {
            setTimeout(runAllTests, 500);
        });
        
        // تشغيل فوري
        runAllTests();
    </script>
</body>
</html>
