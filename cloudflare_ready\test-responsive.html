<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>اختبار التجاوب - Test Responsive</title>
    
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            width: 100%;
            overflow-x: hidden;
            max-width: 100vw;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .test-section {
            background: #2d2d2d;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #444;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 20px 0;
        }
        
        .test-item {
            background: #444;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .test-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .test-button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        
        .test-text {
            line-height: 1.6;
            margin: 15px 0;
        }
        
        .screen-info {
            background: #0066cc;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-grid {
                grid-template-columns: 1fr 1fr;
                gap: 0.5rem;
            }
            
            .test-section {
                padding: 15px;
            }
            
            .test-button {
                padding: 12px 20px;
                font-size: 14px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 5px;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .test-section {
                padding: 10px;
            }
            
            .test-button {
                padding: 10px 15px;
                font-size: 13px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            h2 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار التصميم المتجاوب</h1>
        <p>هذه صفحة اختبار للتأكد من أن التصميم المتجاوب يعمل بشكل صحيح على جميع الأجهزة</p>
        
        <div class="screen-info" id="screen-info">
            <strong>معلومات الشاشة:</strong>
            <span id="screen-details"></span>
        </div>
        
        <div class="test-section">
            <h2>📱 اختبار الشبكة (Grid)</h2>
            <div class="test-grid">
                <div class="test-item">عنصر 1</div>
                <div class="test-item">عنصر 2</div>
                <div class="test-item">عنصر 3</div>
                <div class="test-item">عنصر 4</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ اختبار الصور</h2>
            <div class="test-image">
                صورة تجريبية متجاوبة
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔘 اختبار الأزرار</h2>
            <button class="test-button">زر تجريبي 1</button>
            <button class="test-button">زر تجريبي 2</button>
        </div>
        
        <div class="test-section">
            <h2>📝 اختبار النصوص</h2>
            <div class="test-text">
                هذا نص تجريبي طويل للتأكد من أن النصوص تتكيف بشكل صحيح مع أحجام الشاشات المختلفة. 
                يجب أن يكون هذا النص قابلاً للقراءة على جميع الأجهزة بدون تجاوز حدود الشاشة أو التسبب في تمرير أفقي.
                النص العربي يجب أن يظهر بشكل صحيح من اليمين إلى اليسار.
            </div>
        </div>
        
        <div class="test-section">
            <h2>✅ نتائج الاختبار</h2>
            <div id="test-results">
                <p>🔄 جاري إجراء الاختبارات...</p>
            </div>
        </div>
    </div>
    
    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const devicePixelRatio = window.devicePixelRatio || 1;
            const orientation = width > height ? 'أفقي' : 'عمودي';
            
            let deviceType = 'سطح المكتب';
            if (width <= 480) deviceType = 'هاتف صغير';
            else if (width <= 768) deviceType = 'هاتف/تابلت';
            else if (width <= 1024) deviceType = 'تابلت كبير';
            
            document.getElementById('screen-details').innerHTML = `
                العرض: ${width}px | الارتفاع: ${height}px | 
                النوع: ${deviceType} | الاتجاه: ${orientation} | 
                نسبة البكسل: ${devicePixelRatio}
            `;
        }
        
        function runTests() {
            const results = [];
            
            // اختبار عدم وجود تمرير أفقي
            const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
            results.push({
                test: 'عدم وجود تمرير أفقي',
                passed: !hasHorizontalScroll,
                message: hasHorizontalScroll ? 'يوجد تمرير أفقي!' : 'لا يوجد تمرير أفقي ✅'
            });
            
            // اختبار viewport
            const viewport = document.querySelector('meta[name="viewport"]');
            results.push({
                test: 'وجود viewport meta tag',
                passed: !!viewport,
                message: viewport ? 'viewport موجود ✅' : 'viewport مفقود ❌'
            });
            
            // اختبار box-sizing
            const testElement = document.querySelector('.test-item');
            const boxSizing = window.getComputedStyle(testElement).boxSizing;
            results.push({
                test: 'box-sizing: border-box',
                passed: boxSizing === 'border-box',
                message: boxSizing === 'border-box' ? 'box-sizing صحيح ✅' : 'box-sizing خاطئ ❌'
            });
            
            // اختبار الشبكة
            const grid = document.querySelector('.test-grid');
            const gridColumns = window.getComputedStyle(grid).gridTemplateColumns;
            const expectedColumns = window.innerWidth <= 480 ? 1 : (window.innerWidth <= 768 ? 2 : 2);
            const actualColumns = gridColumns.split(' ').length;
            results.push({
                test: 'تكيف الشبكة',
                passed: actualColumns === expectedColumns,
                message: `الشبكة: ${actualColumns} أعمدة (متوقع: ${expectedColumns}) ${actualColumns === expectedColumns ? '✅' : '❌'}`
            });
            
            // عرض النتائج
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = results.map(result => 
                `<p style="color: ${result.passed ? '#4CAF50' : '#f44336'}">
                    ${result.test}: ${result.message}
                </p>`
            ).join('');
            
            // إضافة ملخص
            const passedTests = results.filter(r => r.passed).length;
            const totalTests = results.length;
            resultsDiv.innerHTML += `
                <hr style="margin: 20px 0; border-color: #444;">
                <p style="font-weight: bold; color: ${passedTests === totalTests ? '#4CAF50' : '#ff9800'}">
                    النتيجة النهائية: ${passedTests}/${totalTests} اختبارات نجحت
                    ${passedTests === totalTests ? '🎉' : '⚠️'}
                </p>
            `;
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        window.addEventListener('load', function() {
            updateScreenInfo();
            setTimeout(runTests, 500);
        });
        
        // تحديث المعلومات عند تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            updateScreenInfo();
            setTimeout(runTests, 300);
        });
        
        // تحديث عند تغيير الاتجاه
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                updateScreenInfo();
                runTests();
            }, 500);
        });
    </script>
</body>
</html>
