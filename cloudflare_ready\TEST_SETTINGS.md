# اختبار إعدادات البوت 🧪

## قائمة الإعدادات المتاحة ✅

### 1. **إعدادات تخصيص الصفحة** 🎨

#### أ) الألوان المخصصة:
- ✅ `custom_bg_color` - لون الخلفية
- ✅ `custom_header_color` - لون الهيدر
- ✅ `custom_button_color` - لون الأزرار
- ✅ `custom_text_color` - لون النص
- ✅ `custom_accent_color` - لون التمييز
- ✅ `custom_card_color` - لون البطاقات
- ✅ `custom_border_color` - لون الحدود

#### ب) النصوص والمحتوى:
- ✅ `site_name` - اسم الموقع/القناة
- ✅ `download_button_text_ar` - نص زر التحميل بالعربية
- ✅ `download_button_text_en` - نص زر التحميل بالإنجليزية

#### ج) الشعارات والصور:
- ✅ `channel_logo_url` - رابط شعار القناة
- ✅ `logo_position` - موضع الشعار (left/right)

#### د) القوالب والستايلات:
- ✅ `style_template` - قالب التصميم

### 2. **إعدادات الإعلانات** 💰

#### أ) تفعيل/إلغاء الإعلانات:
- ✅ `ads_enabled` - تفعيل الإعلانات

#### ب) أنواع الإعلانات:
- ✅ إعلانات قبل التحميل
- ✅ إعلانات منبثقة
- ✅ إعلانات بانر

### 3. **إعدادات القناة** 📺

#### أ) معلومات القناة:
- ✅ `channel_id` - معرف القناة
- ✅ `channel_name` - اسم القناة (يتم جلبه تلقائياً)
- ✅ `channel_logo` - شعار القناة (يتم جلبه تلقائياً)

#### ب) إعدادات النشر:
- ✅ `posting_interval` - فترة النشر
- ✅ `language` - لغة القناة

## كيفية اختبار الإعدادات 🔍

### 1. اختبار تخصيص الصفحة:

```bash
# اذهب إلى البوت
/start

# اختر: إعدادات القناة
# ثم: تخصيص صفحة المود
# ثم: تعديل اسم الموقع

# أدخل اسماً جديداً واختبر التحديث
```

### 2. اختبار الألوان المخصصة:

```bash
# في البوت:
# إعدادات القناة → تخصيص صفحة المود → تخصيص الإعدادات → ثيم مخصص

# جرب تغيير الألوان وتحقق من التطبيق في الصفحة
```

### 3. اختبار شعار القناة:

```bash
# عند ربط قناة جديدة:
# 1. تأكد من أن القناة لها صورة شخصية
# 2. اربط القناة بالبوت
# 3. تحقق من ظهور الشعار في الصفحة
```

### 4. اختبار الإعلانات:

```bash
# في البوت:
# إعدادات القناة → إعدادات الإعلانات
# فعل الإعلانات واختبر ظهورها في الصفحة
```

## الملفات المسؤولة عن كل إعداد 📁

### `script.js`:
- `loadPageCustomization()` - تحميل إعدادات التخصيص
- `applyCustomization()` - تطبيق التخصيصات
- `loadAdsSettings()` - تحميل إعدادات الإعلانات

### `main.py`:
- `user_edit_site_name()` - تعديل اسم الموقع
- `update_channel_info_in_customization()` - تحديث معلومات القناة
- `user_customize_settings()` - قائمة تخصيص الإعدادات

### `supabase_client.py`:
- `update_user_page_customization()` - حفظ إعدادات التخصيص
- `get_user_page_customization_settings()` - جلب إعدادات التخصيص

## اختبار شامل للإعدادات 🎯

### الخطوة 1: اختبار التحديث التلقائي
1. احذف قناة موجودة من البوت
2. أضف قناة جديدة لها اسم وشعار
3. تحقق من تحديث اسم الصفحة تلقائياً
4. تحقق من ظهور شعار القناة

### الخطوة 2: اختبار التعديل اليدوي
1. اذهب إلى تخصيص صفحة المود
2. اختر "تعديل اسم الموقع"
3. أدخل اسماً جديداً
4. تحقق من التحديث في الصفحة

### الخطوة 3: اختبار الألوان
1. اذهب إلى تخصيص الإعدادات
2. اختر "ثيم مخصص"
3. غير الألوان المختلفة
4. تحقق من التطبيق في الصفحة

### الخطوة 4: اختبار الإعلانات
1. فعل الإعلانات من الإعدادات
2. اذهب إلى صفحة مود
3. اضغط على زر التحميل
4. تحقق من ظهور الإعلان

## مشاكل محتملة وحلولها 🔧

### المشكلة: لا يظهر اسم القناة
**الحل:**
- تأكد من أن البوت له صلاحية قراءة معلومات القناة
- تحقق من أن القناة عامة أو أن البوت مضاف كمشرف

### المشكلة: لا يظهر شعار القناة
**الحل:**
- تأكد من أن القناة لها صورة شخصية
- تحقق من صلاحيات البوت لتحميل الملفات

### المشكلة: الألوان لا تتغير
**الحل:**
- امسح كاش المتصفح
- تحقق من وجود أخطاء في وحدة التحكم
- تأكد من صحة أكواد الألوان

### المشكلة: الإعلانات لا تظهر
**الحل:**
- تحقق من تفعيل الإعلانات في الإعدادات
- تأكد من وجود محتوى إعلاني
- تحقق من عدم وجود مانع إعلانات

## نصائح للاختبار الفعال 💡

1. **استخدم متصفحات مختلفة** للاختبار
2. **اختبر على الهاتف والكمبيوتر**
3. **امسح الكاش** بين الاختبارات
4. **راقب وحدة التحكم** للأخطاء
5. **اختبر مع قنوات مختلفة**
6. **تحقق من سجلات البوت**

---

**ملاحظة:** جميع الإعدادات تعمل بشكل متزامن ولا تتعارض مع بعضها البعض.
