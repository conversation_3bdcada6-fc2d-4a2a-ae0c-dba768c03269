/*
 * بديل Tailwind CSS - فقط الفئات المستخدمة
 * Tailwind CSS Replacement - Only used classes
 */

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    box-sizing: border-box;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

/* Padding */
.p-4 {
    padding: 1rem;
}

.p-1 {
    padding: 0.25rem;
}

.pb-24 {
    padding-bottom: 6rem;
}

/* Margin */
.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

/* Grid */
.grid {
    display: grid;
    width: 100%;
    box-sizing: border-box;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
    gap: 1rem;
}

/* Flexbox */
.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.items-center {
    align-items: center;
}

/* Text */
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.font-bold {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

/* Position */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.bottom-0 {
    bottom: 0;
}

.left-0 {
    left: 0;
}

.right-0 {
    right: 0;
}

/* Size */
.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

/* Z-index */
.z-50 {
    z-index: 50;
}

/* Background */
.bg-gray-800 {
    background-color: #1f2937;
}

/* Border radius */
.rounded-lg {
    border-radius: 0.5rem;
}

/* Overflow */
.overflow-hidden {
    overflow: hidden;
}

/* Object fit */
.object-cover {
    object-fit: cover;
}

/* List */
.list-decimal {
    list-style-type: decimal;
}

.list-inside {
    list-style-position: inside;
}

/* Display */
.hidden {
    display: none;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        gap: 0.75rem !important;
    }

    .gap-4 {
        gap: 0.75rem;
    }

    .p-4 {
        padding: 0.75rem;
    }

    .text-2xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }

    .text-xl {
        font-size: 1.125rem;
        line-height: 1.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
    
    .grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .p-4 {
        padding: 0.5rem;
    }
    
    .text-2xl {
        font-size: 1.125rem;
        line-height: 1.5rem;
    }
    
    .text-xl {
        font-size: 1rem;
        line-height: 1.25rem;
    }
    
    .pb-24 {
        padding-bottom: 4rem;
    }
}

/* إصلاحات إضافية */
.grid > * {
    min-width: 0;
    box-sizing: border-box;
}

.flex > * {
    box-sizing: border-box;
}

/* إصلاح للحاويات */
.container,
.grid,
.flex {
    max-width: 100%;
    overflow-x: hidden;
}

/* إصلاح للنصوص */
.text-2xl,
.text-xl,
.font-bold {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* إصلاح للصور */
.object-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* إصلاح للعناصر المطلقة */
.absolute {
    max-width: 100%;
    box-sizing: border-box;
}

/* إصلاح للعناصر الثابتة */
.fixed {
    max-width: 100vw;
    box-sizing: border-box;
}

.fixed.bottom-0 {
    width: 100vw;
    left: 0;
    right: 0;
}

/* إصلاح خاص للهواتف */
@media (max-width: 768px) {
    * {
        max-width: 100vw;
        box-sizing: border-box;
    }
    
    .container {
        width: 100vw;
        max-width: 100vw;
        margin: 0;
        padding: 0.5rem;
    }
    
    .grid {
        width: 100%;
        max-width: 100%;
    }
    
    .flex {
        width: 100%;
        max-width: 100%;
    }
}
